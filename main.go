package main

import (
	"bufio"
	"log"
	"lsp/rpc"
	"os"
)

func main() {
	logger := getLogger("C:/Users/<USER>/Go/language_server_protocol/log.txt")
	logger.Println("Starting!")

	scanner := bufio.NewScanner(os.Stdin)
	scanner.Split(rpc.Split)

	logger.Println("Scanner created, waiting for input...")

	for scanner.Scan() {
		logger.Println("Scanner found a message!")
		msg := scanner.Bytes()
		handleMessage(logger, msg)
	}
}

func handleMessage(logger *log.Logger, msg []byte) {
	method, _, err := rpc.DecodeMessage(msg)

	if err != nil {
		logger.Printf("Decode error: %v", err)
		return
	}

	logger.Printf("Method received: %s", method)
}

func getLogger(filename string) *log.Logger {
	logfile, err := os.OpenFile(filename, os.O_CREATE|os.O_TRUNC|os.O_WRONLY, 0666)

	if err != nil {
		panic("the file isn't even good bro")
	}

	return log.New(logfile, "[lsp]", log.Ldate|log.Ltime|log.Lshortfile)
}
