package rpc_test

import (
	"fmt"
	"lsp/rpc"
	"testing"
)

type EncodingTest struct {
	Testing bool
}

func TestEncode(t *testing.T) {
	expected := "Content-Length: 16 \r\n\r\n{\"Testing\":true}"
	actual := rpc.EncodeMessage(EncodingTest{Testing: true})
	if expected != actual {
		t.Fatalf("Expected: %s, Actual: %s", expected, actual)
	}
}

func TestDecode(t *testing.T) {

	message := "Content-Length: 15\r\n\r\n{\"Method\":\"hi\"}"
	method, content, err := rpc.DecodeMessage([]byte(message))
	contentLength := len(content)
	fmt.Printf("Method: %s, Content: %s", method, string(content))
	if err != nil {
		t.Fatal(err)
	}

	if contentLength != 15 {
		t.Fatalf("Expected 15, got %d", contentLength)
	}

	if method != "hi" {
		t.<PERSON>alf("Expected: 'hi', got %s", method)
	}
}
